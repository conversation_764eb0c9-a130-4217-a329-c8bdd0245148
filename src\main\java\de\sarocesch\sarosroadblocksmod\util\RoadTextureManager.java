package de.sarocesch.sarosroadblocksmod.util;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.Level;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;
import de.sarocesch.sarosroadblocksmod.block.entity.RoadBlockEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * Verwaltet die Texturen für Straßenblöcke
 */
public class RoadTextureManager {
    // Singleton-Instanz
    private static RoadTextureManager instance;

    // Map für die Textur-Ressourcen
    private final Map<String, ResourceLocation> textureMap = new HashMap<>();

    // Private Konstruktor für Singleton
    private RoadTextureManager() {
        registerDefaultTextures();
    }

    /**
     * Gibt die Singleton-Instanz zurück
     */
    public static RoadTextureManager getInstance() {
        if (instance == null) {
            instance = new RoadTextureManager();
        }
        return instance;
    }

    /**
     * Registriert die Standard-Texturen
     */
    private void registerDefaultTextures() {
        // Basis-Texturen
        registerTexture("default", "block/road_block_fine");
        registerTexture("straight", "block/road_block_linie");
        registerTexture("corner", "block/road_block_linie_ecke");
        registerTexture("cross", "block/road_block_kreuzung");

        // Weitere Texturen aus dem vorhandenen System
        registerTexture("two", "block/two");
        registerTexture("tt", "block/tt");
        registerTexture("tf", "block/tf");
        registerTexture("ts", "block/ts");
        registerTexture("tse", "block/tse");
        registerTexture("te", "block/te");
        registerTexture("nt", "block/nt");
        registerTexture("thirty", "block/thirty");
        registerTexture("ohae", "block/ohae");
        registerTexture("ohat", "block/ohat");
        registerTexture("tta", "block/tta");
        registerTexture("tts", "block/tts");
        registerTexture("ttd", "block/ttd");
        registerTexture("ttf", "block/ttf");
        registerTexture("ttg", "block/ttg");
        registerTexture("tth", "block/tth");
        registerTexture("ttj", "block/ttj");
        registerTexture("ttk", "block/ttk");
        registerTexture("corner_straight", "block/corner_straight");

        // Hier können weitere Texturen hinzugefügt werden
    }

    /**
     * Registriert eine Textur
     */
    public void registerTexture(String id, String texturePath) {
        textureMap.put(id, ResourceLocation.fromNamespaceAndPath(SarosRoadBlocksModMod.MODID, texturePath));
    }

    /**
     * Gibt die Textur-ResourceLocation für eine ID zurück
     */
    public ResourceLocation getTexture(String id) {
        return textureMap.getOrDefault(id, textureMap.get("default"));
    }

    /**
     * Speichert die Textur-ID in den NBT-Daten eines BlockEntity
     */
    public static void setTextureForBlockEntity(BlockEntity blockEntity, String textureId) {
        if (blockEntity instanceof RoadBlockEntity roadBlockEntity) {
            roadBlockEntity.setTextureId(textureId);
            blockEntity.setChanged();
        }
    }

    /**
     * Liest die Textur-ID aus den NBT-Daten eines BlockEntity
     */
    public static String getTextureFromBlockEntity(BlockEntity blockEntity) {
        if (blockEntity instanceof RoadBlockEntity roadBlockEntity) {
            return roadBlockEntity.getTextureId();
        }
        return "default";
    }

    /**
     * Hilfsmethode zum Setzen der Textur für einen Block an einer Position
     */
    public static void setTextureForBlock(Level level, BlockPos pos, String textureId) {
        if (level.isClientSide()) {
            // Auf Client-Seite nichts tun, da der Server die Änderungen sendet
            return;
        }

        BlockEntity blockEntity = level.getBlockEntity(pos);
        if (blockEntity instanceof RoadBlockEntity roadBlockEntity) {
            // Die setTextureId-Methode kümmert sich um die Synchronisation
            roadBlockEntity.setTextureId(textureId);

            // Stelle sicher, dass die BlockEntity als geändert markiert ist
            blockEntity.setChanged();

            // Sende ein explizites Update an alle Clients
            BlockState state = level.getBlockState(pos);
            level.sendBlockUpdated(pos, state, state, 3);

            // Markiere den Chunk als geändert, damit er gespeichert wird
            level.getChunkAt(pos).setUnsaved(true);
        }
    }

    /**
     * Hilfsmethode zum Lesen der Textur für einen Block an einer Position
     */
    public static String getTextureForBlock(Level level, BlockPos pos) {
        BlockEntity blockEntity = level.getBlockEntity(pos);
        if (blockEntity instanceof RoadBlockEntity) {
            return getTextureFromBlockEntity(blockEntity);
        }
        return "default";
    }
}
