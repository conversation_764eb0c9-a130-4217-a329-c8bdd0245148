package de.sarocesch.sarosroadblocksmod.init;

import net.minecraft.world.level.block.entity.BlockEntityType;
import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.minecraft.core.registries.BuiltInRegistries;

import de.sarocesch.sarosroadblocksmod.block.entity.RoadBlockEntity;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModBlockEntities {
    public static final DeferredRegister<BlockEntityType<?>> REGISTRY = DeferredRegister.create(BuiltInRegistries.BLOCK_ENTITY_TYPE, SarosRoadBlocksModMod.MODID);

    public static final DeferredHolder<BlockEntityType<?>, BlockEntityType<RoadBlockEntity>> ROAD_BLOCK_ENTITY = REGISTRY.register("road_block_entity",
            () -> BlockEntityType.Builder.of(RoadBlockEntity::new,
                    SarosRoadBlocksModModBlocks.ASPHALT.get()
                    // Hier können weitere Blöcke hinzugefügt werden, die diese BlockEntity verwenden
                    ).build(null));
}
