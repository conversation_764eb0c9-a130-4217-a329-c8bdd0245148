package de.sarocesch.sarosroadblocksmod.procedures;

import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosroadblocksmod.block.SidewalkBlock;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlocks;
import net.minecraft.world.level.block.state.properties.Property;

import java.util.Map;

public class StreetLightPoleBlockAddedProcedure {
    public static void execute(LevelAccessor world, double x, double y, double z) {
        BlockPos posBelow = BlockPos.containing(x, y - 1, z);
        BlockState blockStateBelow = world.getBlockState(posBelow);

        // Überprüfung, ob der Block unten ASPHALT ist
        if (blockStateBelow.getBlock() == SarosRoadBlocksModModBlocks.ASPHALT.get()) {
            // Verwende die AsphaltBlock-Variante zur Überprüfung
            if (blockStateBelow.getValue(AsphaltBlock.VARIANT) != AsphaltBlock.Variant.DEFAULT) {
                placeStreetLightPole(world, x, y, z);
            }
        }

        // Überprüfung, ob der Block unten SIDEWALK ist
        else if (blockStateBelow.getBlock() == SarosRoadBlocksModModBlocks.SIDEWALK.get()) {
            // Verwende die SidewalkBlock-Variante zur Überprüfung
            if (blockStateBelow.getValue(SidewalkBlock.VARIANT) != SidewalkBlock.Variant.DEFAULT) {
                placeStreetLightPole(world, x, y, z);
            }
        }
    }

    // Methode zum Platzieren des STREET_LIGHT_POLE_2 Blocks
    private static void placeStreetLightPole(LevelAccessor world, double x, double y, double z) {
        BlockPos _bp = BlockPos.containing(x, y, z);
        BlockState _bs = SarosRoadBlocksModModBlocks.STREET_LIGHT_POLE_2.get().defaultBlockState();
        BlockState _bso = world.getBlockState(_bp);

        // Kopiere alle Blockzustände
        for (Map.Entry<Property<?>, Comparable<?>> entry : _bso.getValues().entrySet()) {
            Property _property = _bs.getBlock().getStateDefinition().getProperty(entry.getKey().getName());
            if (_property != null && _bs.getValue(_property) != null) {
                try {
                    _bs = _bs.setValue(_property, (Comparable) entry.getValue());
                } catch (Exception e) {
                    // Optional: Fehlerbehandlung hier
                }
            }
        }

        // Setze den Block im Level
        world.setBlock(_bp, _bs, 3);
    }
}
