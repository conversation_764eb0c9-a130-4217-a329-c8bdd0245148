package de.sarocesch.sarosroadblocksmod.block;

import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.InteractionResultHolder;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.util.StringRepresentable;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.world.level.block.HorizontalDirectionalBlock;
import net.minecraft.core.Direction;
import net.minecraft.client.resources.model.Material;

public class SidewalkBlock extends Block {
	    public static final DirectionProperty FACING = HorizontalDirectionalBlock.FACING;
    public static final EnumProperty<Variant> VARIANT = EnumProperty.create("variant", Variant.class);

    public SidewalkBlock() {
        super(BlockBehaviour.Properties.of().sound(SoundType.STONE).strength(3f, 20f));
        this.registerDefaultState(this.stateDefinition.any().setValue(FACING, Direction.NORTH).setValue(VARIANT, Variant.DEFAULT));
    }

    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(FACING, VARIANT);
    }

        @Override
    public BlockState getStateForPlacement(BlockPlaceContext context) {
        return this.defaultBlockState()
                .setValue(FACING, context.getHorizontalDirection().getOpposite())
                .setValue(VARIANT, Variant.DEFAULT);
    }


@Override
public VoxelShape getShape(BlockState state, BlockGetter world, BlockPos pos, CollisionContext context) {
    int height = getHeightForVariant(state.getValue(VARIANT));
    return switch (state.getValue(FACING)) {
        case NORTH -> box(0, 0, 0, 16, height, 16);
        case EAST -> box(0, 0, 0, 16, height, 16);
        case SOUTH -> box(0, 0, 0, 16, height, 16);
        case WEST -> box(0, 0, 0, 16, height, 16);
        default -> box(0, 0, 0, 16, height, 16);
    };
}


private int getHeightForVariant(Variant variant) {
    return switch (variant) {
        case ONE -> 15;
        case TWO -> 14;
        case THREE -> 13;
        case FOUR -> 12;
        case FIVE -> 11;
        case SIX -> 10;
        case SEVEN -> 9;
        case EIGHT -> 8;
        case NINE -> 7;
        case TEN -> 6;
        case ELEVEN -> 5;
        case TWELVE -> 4;
        case THIRTEEN -> 3;
        case FOURTEEN -> 2;
        case FIFTEEN -> 1;
        default -> 16; // Für "default" oder unbekannte Varianten
    };
}

    private Variant getNextVariant(Variant current) {
        Variant[] variants = Variant.values();
        int currentIndex = current.ordinal();
        return variants[(currentIndex + 1) % variants.length];
    }

    @Override
    public BlockState rotate(BlockState state, Rotation rot) {
        return state;
    }

    @Override
    public BlockState mirror(BlockState state, Mirror mirrorIn) {
        return state;
    }

    public enum Variant implements StringRepresentable {
        DEFAULT("default"),
        ONE("1"),
        TWO("2"),
        THREE("3"),
        FOUR("4"),
        FIVE("5"),
        SIX("6"),
        SEVEN("7"),
        EIGHT("8"),
        NINE("9"),
        TEN("10"),
        ELEVEN("11"),
        TWELVE("12"),
        THIRTEEN("13"),
        FOURTEEN("14"),
        FIFTEEN("15");

        private final String name;

        Variant(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }
    }
}
