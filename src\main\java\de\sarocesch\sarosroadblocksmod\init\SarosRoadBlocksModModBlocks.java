package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.minecraft.core.registries.BuiltInRegistries;

import net.minecraft.world.level.block.Block;

import de.sarocesch.sarosroadblocksmod.block.StreetLightPoleBlock;
import de.sarocesch.sarosroadblocksmod.block.StreetLightPole2Block;
import de.sarocesch.sarosroadblocksmod.block.StreetLightBlock;
import de.sarocesch.sarosroadblocksmod.block.SidewalkBlock;
import de.sarocesch.sarosroadblocksmod.block.GuardRailEndBlock;
import de.sarocesch.sarosroadblocksmod.block.GuardRailEnd2Block;
import de.sarocesch.sarosroadblocksmod.block.CrashBarrierPoleBlock;
import de.sarocesch.sarosroadblocksmod.block.CrashBarrierCornerBlock;
import de.sarocesch.sarosroadblocksmod.block.CrashBarrierCorner2Block;
import de.sarocesch.sarosroadblocksmod.block.CrashBarrierBlock;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModBlocks {
	public static final DeferredRegister<Block> REGISTRY = DeferredRegister.create(BuiltInRegistries.BLOCK, SarosRoadBlocksModMod.MODID);
	public static final DeferredHolder<Block, Block> ASPHALT = REGISTRY.register("asphalt", () -> new AsphaltBlock());
	public static final DeferredHolder<Block, Block> CRASH_BARRIER = REGISTRY.register("crash_barrier", () -> new CrashBarrierBlock());
	public static final DeferredHolder<Block, Block> CRASH_BARRIER_POLE = REGISTRY.register("crash_barrier_pole", () -> new CrashBarrierPoleBlock());
	public static final DeferredHolder<Block, Block> STREET_LIGHT = REGISTRY.register("street_light", () -> new StreetLightBlock());
	public static final DeferredHolder<Block, Block> STREET_LIGHT_POLE = REGISTRY.register("street_light_pole", () -> new StreetLightPoleBlock());
	public static final DeferredHolder<Block, Block> CRASH_BARROER_CORNER = REGISTRY.register("crash_barroer_corner", () -> new CrashBarrierCornerBlock());
	public static final DeferredHolder<Block, Block> CRASH_BARRIER_CORNER_2 = REGISTRY.register("crash_barrier_corner_2", () -> new CrashBarrierCorner2Block());
	public static final DeferredHolder<Block, Block> SIDEWALK = REGISTRY.register("sidewalk", () -> new SidewalkBlock());
	public static final DeferredHolder<Block, Block> GUARD_RAIL_END = REGISTRY.register("guard_rail_end", () -> new GuardRailEndBlock());
	public static final DeferredHolder<Block, Block> GUARD_RAIL_END_2 = REGISTRY.register("guard_rail_end_2", () -> new GuardRailEnd2Block());
	public static final DeferredHolder<Block, Block> STREET_LIGHT_POLE_2 = REGISTRY.register("street_light_pole_2", () -> new StreetLightPole2Block());
}
