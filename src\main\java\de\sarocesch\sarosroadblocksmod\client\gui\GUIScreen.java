package de.sarocesch.sarosroadblocksmod.client.gui;

import net.minecraft.network.protocol.Packet;
import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.network.chat.Component;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.components.ImageButton;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.WidgetSprites;
import net.minecraft.client.Minecraft;
import net.minecraft.network.Connection;

import de.sarocesch.sarosroadblocksmod.world.inventory.GUIMenu;
import de.sarocesch.sarosroadblocksmod.network.GUIButtonMessage;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

import java.util.HashMap;

import com.mojang.blaze3d.systems.RenderSystem;

import net.minecraft.network.protocol.common.ServerboundCustomPayloadPacket;

public class GUIScreen extends AbstractContainerScreen<GUIMenu> {
	private final static HashMap<String, Object> guistate = GUIMenu.guistate;
	private final Level world;
	private final int x, y, z;
	private final Player entity;
	Button button_rotate;
	Button button_remove;
	ImageButton imagebutton_road_block_linie;
	ImageButton imagebutton_road_block_linie1;
	ImageButton imagebutton_d;
	ImageButton imagebutton_road_block_linie_cross;
	ImageButton imagebutton_23;
	ImageButton imagebutton_24;
	ImageButton imagebutton_26;
	ImageButton imagebutton_27;
	ImageButton imagebutton_28;
	ImageButton imagebutton_29;
	ImageButton imagebutton_30;
	ImageButton imagebutton_108;
	ImageButton imagebutton_110;
	ImageButton imagebutton_2;
	ImageButton imagebutton_tt0;
	ImageButton imagebutton_tt1;
	ImageButton imagebutton_tt2;
	ImageButton imagebutton_tt3;
	ImageButton imagebutton_tt4;
	ImageButton imagebutton_tt5;
	ImageButton imagebutton_tt6;
	ImageButton imagebutton_tt7;

	public GUIScreen(GUIMenu container, Inventory inventory, Component text) {
		super(container, inventory, text);
		this.world = container.world;
		this.x = container.x;
		this.y = container.y;
		this.z = container.z;
		this.entity = container.entity;
		this.imageWidth = 500;
		this.imageHeight = 436;
	}

	@Override
	public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTicks) {
		this.renderBackground(guiGraphics, mouseX, mouseY, partialTicks);
		super.render(guiGraphics, mouseX, mouseY, partialTicks);
		this.renderTooltip(guiGraphics, mouseX, mouseY);
	}

	@Override
	protected void renderBg(GuiGraphics guiGraphics, float partialTicks, int mouseX, int mouseY) {
		RenderSystem.setShaderColor(1, 1, 1, 1);
		RenderSystem.enableBlend();
		RenderSystem.defaultBlendFunc();

		// Draw textures for each button with hover effect
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 106, "textures/screens/road_block_linie.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 106, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 143, "textures/screens/road_block_linie_ecke.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 143, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 180, "textures/screens/road_block_linie_grad_eck.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 180, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 217, "textures/screens/road_block_linie_cross.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 217, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 217, "textures/screens/23.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 217, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 291, "textures/screens/24.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 291, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 106, "textures/screens/26.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 106, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 254, "textures/screens/27.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 254, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 143, "textures/screens/28.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 143, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 180, "textures/screens/29.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 180, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 42, this.topPos + 254, "textures/screens/30.png", isHovering(mouseX, mouseY, this.leftPos + 42, this.topPos + 254, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 106, "textures/screens/108.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 106, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 143, "textures/screens/110.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 143, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 79, this.topPos + 291, "textures/screens/2.png", isHovering(mouseX, mouseY, this.leftPos + 79, this.topPos + 291, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 180, "textures/screens/tt0.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 180, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 217, "textures/screens/tt1.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 217, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 254, "textures/screens/tt2.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 254, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 116, this.topPos + 291, "textures/screens/tt3.png", isHovering(mouseX, mouseY, this.leftPos + 116, this.topPos + 291, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 153, this.topPos + 106, "textures/screens/tt4.png", isHovering(mouseX, mouseY, this.leftPos + 153, this.topPos + 106, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 153, this.topPos + 143, "textures/screens/tt5.png", isHovering(mouseX, mouseY, this.leftPos + 153, this.topPos + 143, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 153, this.topPos + 180, "textures/screens/tt6.png", isHovering(mouseX, mouseY, this.leftPos + 153, this.topPos + 180, 32, 32));
		drawButtonTexture(guiGraphics, this.leftPos + 153, this.topPos + 217, "textures/screens/tt7.png", isHovering(mouseX, mouseY, this.leftPos + 153, this.topPos + 217, 32, 32));

		RenderSystem.disableBlend();
	}

	private boolean isHovering(int mouseX, int mouseY, int x, int y, int width, int height) {
		return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
	}

	private void drawButtonTexture(GuiGraphics guiGraphics, int x, int y, String texturePath, boolean isHovered) {
		ResourceLocation texture = ResourceLocation.fromNamespaceAndPath("saros_road_blocks_mod", texturePath);

		// Draw a highlight behind the button if it's being hovered over
		if (isHovered) {
			RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
			guiGraphics.fill(x, y, x + 32, y + 32, 0x80FFFFFF); // Semi-transparent white highlight
		}

		// Draw the button texture
		guiGraphics.blit(texture, x, y, 0, 0, 32, 32, 32, 32);

		// Draw a border around the button if it's being hovered over
		if (isHovered) {
			// Draw a white border manually
			int color = 0xFFFFFFFF; // White color

			// Top border
			guiGraphics.fill(x, y, x + 32, y + 1, color);
			// Bottom border
			guiGraphics.fill(x, y + 31, x + 32, y + 32, color);
			// Left border
			guiGraphics.fill(x, y, x + 1, y + 32, color);
			// Right border
			guiGraphics.fill(x + 31, y, x + 32, y + 32, color);
		}
	}

	@Override
	public boolean keyPressed(int key, int b, int c) {
		if (key == 256) {
			this.minecraft.player.closeContainer();
			return true;
		}
		return super.keyPressed(key, b, c);
	}

	@Override
	public void containerTick() {
		super.containerTick();
	}

	@Override
	protected void renderLabels(GuiGraphics guiGraphics, int mouseX, int mouseY) {
	}

	@Override
	public void onClose() {
		super.onClose();
	}

	@Override
	public void init() {
		super.init();
		button_rotate = Button.builder(Component.translatable("gui.saros_road_blocks_mod.gui.button_rotate"), e -> {
			if (true) {
				sendButtonMessage(0);
				GUIButtonMessage.handleButtonAction(entity, 0, x, y, z);
			}
		}).bounds(this.leftPos + 395, this.topPos + 109, 56, 20).build();
		guistate.put("button:button_rotate", button_rotate);
		this.addRenderableWidget(button_rotate);

		button_remove = Button.builder(Component.translatable("gui.saros_road_blocks_mod.gui.button_remove"), e -> {
			if (true) {
				sendButtonMessage(1);
				GUIButtonMessage.handleButtonAction(entity, 1, x, y, z);
			}
		}).bounds(this.leftPos + 395, this.topPos + 138, 56, 20).build();
		guistate.put("button:button_remove", button_remove);
		this.addRenderableWidget(button_remove);

		// Create transparent buttons that will trigger actions but won't be visible
		imagebutton_road_block_linie = createInvisibleImageButton(this.leftPos + 42, this.topPos + 106, 2);
		imagebutton_road_block_linie1 = createInvisibleImageButton(this.leftPos + 42, this.topPos + 143, 3);
		imagebutton_d = createInvisibleImageButton(this.leftPos + 42, this.topPos + 180, 4);
		imagebutton_road_block_linie_cross = createInvisibleImageButton(this.leftPos + 42, this.topPos + 217, 5);
		imagebutton_23 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 217, 6);
		imagebutton_24 = createInvisibleImageButton(this.leftPos + 42, this.topPos + 291, 7);
		imagebutton_26 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 106, 8);
		imagebutton_27 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 254, 9);
		imagebutton_28 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 143, 10);
		imagebutton_29 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 180, 11);
		imagebutton_30 = createInvisibleImageButton(this.leftPos + 42, this.topPos + 254, 12);
		imagebutton_108 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 106, 13);
		imagebutton_110 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 143, 14);
		imagebutton_2 = createInvisibleImageButton(this.leftPos + 79, this.topPos + 291, 15);
		imagebutton_tt0 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 180, 16);
		imagebutton_tt1 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 217, 17);
		imagebutton_tt2 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 254, 18);
		imagebutton_tt3 = createInvisibleImageButton(this.leftPos + 116, this.topPos + 291, 19);
		imagebutton_tt4 = createInvisibleImageButton(this.leftPos + 153, this.topPos + 106, 20);
		imagebutton_tt5 = createInvisibleImageButton(this.leftPos + 153, this.topPos + 143, 21);
		imagebutton_tt6 = createInvisibleImageButton(this.leftPos + 153, this.topPos + 180, 22);
		imagebutton_tt7 = createInvisibleImageButton(this.leftPos + 153, this.topPos + 217, 23);
	}

	private void sendButtonMessage(int buttonId) {
		Connection connection = Minecraft.getInstance().getConnection().getConnection();
		if (connection != null) {
			// Erstelle die Nachricht
			GUIButtonMessage message = new GUIButtonMessage(buttonId, this.x, this.y, this.z);

			// Sende als registriertes CustomPacketPayload
			connection.send(new ServerboundCustomPayloadPacket(message));
		}
	}

	private ImageButton createInvisibleImageButton(int x, int y, int buttonId) {
		ImageButton button = new ImageButton(
				x, y, 32, 32,
				new WidgetSprites(ResourceLocation.parse("textures/gui/sprites/widget/button.png"),
						ResourceLocation.parse("textures/gui/sprites/widget/button_highlighted.png")),
				e -> {
					sendButtonMessage(buttonId);
					GUIButtonMessage.handleButtonAction(entity, buttonId, this.x, this.y, this.z);
				},
				Component.empty()
		) {
			@Override
			public void renderWidget(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
				// Don't render the button itself, just handle clicks
				// The actual button images are drawn in renderBg
			}
		};

		guistate.put("button:imagebutton_" + buttonId, button);
		this.addRenderableWidget(button);
		return button;
	}
}