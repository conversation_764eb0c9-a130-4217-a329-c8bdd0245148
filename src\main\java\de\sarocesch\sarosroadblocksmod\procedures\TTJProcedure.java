package de.sarocesch.sarosroadblocksmod.procedures;

import net.minecraft.world.item.ItemStack;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity;

import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModItems;
import de.sarocesch.sarosroadblocksmod.util.ItemStackHelper;

public class TTJProcedure {
	public static void execute(Entity entity) {
		if (entity == null)
			return;
		if (SarosRoadBlocksModModItems.BRUSH.get() == (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem()
				|| SarosRoadBlocksModModItems.BRUSH_FULL.get() == (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem()
				|| SarosRoadBlocksModModItems.BRUSH_GRADE.get() == (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem()
				|| SarosRoadBlocksModModItems.BRUSH_ECKE.get() == (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem()
				|| SarosRoadBlocksModModItems.BRUSH_GRAD_ECK.get() == (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY).getItem()) {
			
			ItemStack stack = (entity instanceof LivingEntity _livEnt ? _livEnt.getMainHandItem() : ItemStack.EMPTY);
			ItemStackHelper.setCustomModelData(stack, 1);
			ItemStackHelper.setString(stack, "modi", "ttj");
			if (entity instanceof Player _player)
				_player.closeContainer();
		}
	}
}
