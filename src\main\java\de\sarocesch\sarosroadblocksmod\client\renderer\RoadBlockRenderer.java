package de.sarocesch.sarosroadblocksmod.client.renderer;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.blockentity.BlockEntityRenderer;
import net.minecraft.client.renderer.block.BlockRenderDispatcher;
import net.minecraft.client.renderer.blockentity.BlockEntityRendererProvider.Context;

import de.sarocesch.sarosroadblocksmod.block.entity.RoadBlockEntity;

/**
 * Renderer für die Straßenblöcke
 */
public class RoadBlockRenderer implements BlockEntityRenderer<RoadBlockEntity> {
    private final BlockRenderDispatcher blockRenderer;

    public RoadBlockRenderer(Context context) {
        this.blockRenderer = context.getBlockRenderDispatcher();
    }

    @Override
    public void render(RoadBlockEntity blockEntity, float partialTick, PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, int packedOverlay) {
        // Wir verwenden den Standard-Blockrenderer, da wir die Textur über die BlockState-Variante steuern
        // Die BlockEntity dient nur zur Speicherung der Textur-ID

        // In einer erweiterten Implementierung könnten wir hier ein benutzerdefiniertes Modell rendern,
        // das die Textur direkt aus der BlockEntity liest, anstatt den BlockState zu verwenden

        // String textureId = blockEntity.getTextureId();
        // Hier könnten wir dann die entsprechende Textur laden und anwenden
    }
}
