package de.sarocesch.sarosroadblocksmod.block.entity;

import net.minecraft.core.BlockPos;
import net.minecraft.core.HolderLookup;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.protocol.Packet;
import net.minecraft.network.protocol.game.ClientGamePacketListener;
import net.minecraft.network.protocol.game.ClientboundBlockEntityDataPacket;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlockEntities;

/**
 * BlockEntity für Straßenblöcke, die Texturen speichern können
 */
public class RoadBlockEntity extends BlockEntity {
    private String textureId = "default";

    public RoadBlockEntity(BlockPos pos, BlockState state) {
        super(SarosRoadBlocksModModBlockEntities.ROAD_BLOCK_ENTITY.get(), pos, state);
    }

    /**
     * Setzt die Textur-ID und markiert die BlockEntity als geändert
     */
    public void setTextureId(String textureId) {
        this.textureId = textureId;
        setChanged();

        // Sende ein Update-Paket an die Clients, wenn wir auf dem Server sind
        Level level = getLevel();
        if (level != null && !level.isClientSide()) {
            BlockState state = getBlockState();
            level.sendBlockUpdated(getBlockPos(), state, state, 3);
        }
    }

    /**
     * Gibt die aktuelle Textur-ID zurück
     */
    public String getTextureId() {
        return textureId;
    }

    /**
     * Speichert die Daten in NBT
     */
    @Override
    protected void saveAdditional(CompoundTag tag, HolderLookup.Provider provider) {
        super.saveAdditional(tag, provider);
        tag.putString("TextureId", textureId);
    }

    /**
     * Lädt die Daten aus NBT
     */
    @Override
    public void loadAdditional(CompoundTag tag, HolderLookup.Provider provider) {
        super.loadAdditional(tag, provider);
        if (tag.contains("TextureId")) {
            textureId = tag.getString("TextureId");
        }
    }

    /**
     * Erstellt ein Update-Paket für den Client
     */
    @Override
    public Packet<ClientGamePacketListener> getUpdatePacket() {
        return ClientboundBlockEntityDataPacket.create(this);
    }

    /**
     * Verarbeitet das Update-Paket auf der Client-Seite
     */
    @Override
    public void handleUpdateTag(CompoundTag tag, HolderLookup.Provider provider) {
        super.handleUpdateTag(tag, provider);
        if (tag.contains("TextureId")) {
            textureId = tag.getString("TextureId");
        }

        // Aktualisiere die Rendering-Informationen
        Level level = getLevel();
        if (level != null && level.isClientSide()) {
            level.sendBlockUpdated(getBlockPos(), getBlockState(), getBlockState(), 3);
            level.getModelDataManager().requestRefresh(this);
        }
    }

    /**
     * Erstellt ein NBT-Tag für das Update-Paket
     */
    @Override
    public CompoundTag getUpdateTag(HolderLookup.Provider provider) {
        CompoundTag tag = super.getUpdateTag(provider);
        tag.putString("TextureId", textureId);
        return tag;
    }
}
