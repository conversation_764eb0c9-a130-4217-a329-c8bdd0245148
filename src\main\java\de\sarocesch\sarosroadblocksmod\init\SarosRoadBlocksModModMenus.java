
/*
 *	MCreator note: This file will be REGENERATED on each build.
 */
package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.minecraft.core.registries.BuiltInRegistries;
import net.neoforged.neoforge.common.extensions.IMenuTypeExtension;

import net.minecraft.world.inventory.MenuType;

import de.sarocesch.sarosroadblocksmod.world.inventory.GUIMenu;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModMenus {
	public static final DeferredRegister<MenuType<?>> REGISTRY = DeferredRegister.create(BuiltInRegistries.MENU, SarosRoadBlocksModMod.MODID);
	public static final DeferredHolder<MenuType<?>, MenuType<GUIMenu>> GUI = REGISTRY.register("gui", () -> IMenuTypeExtension.create(GUIMenu::new));
}
