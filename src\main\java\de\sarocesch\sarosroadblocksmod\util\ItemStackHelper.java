package de.sarocesch.sarosroadblocksmod.util;

import net.minecraft.world.item.ItemStack;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.core.component.DataComponents;
import net.minecraft.world.item.component.CustomData;
import net.minecraft.world.item.component.CustomModelData;

/**
 * Hilfsmethoden für ItemStack-Operationen in Minecraft 1.20.6
 */
public class ItemStackHelper {
    
    /**
     * Setzt einen String-Wert in den CustomData-Komponenten eines ItemStacks
     */
    public static void setString(ItemStack stack, String key, String value) {
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        CompoundTag tag = customData != null ? customData.copyTag() : new CompoundTag();
        tag.putString(key, value);
        stack.set(DataComponents.CUSTOM_DATA, CustomData.of(tag));
    }
    
    /**
     * Setzt einen Integer-Wert in den CustomData-Komponenten eines ItemStacks
     */
    public static void setInt(ItemStack stack, String key, int value) {
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        CompoundTag tag = customData != null ? customData.copyTag() : new CompoundTag();
        tag.putInt(key, value);
        stack.set(DataComponents.CUSTOM_DATA, CustomData.of(tag));
    }
    
    /**
     * Setzt einen Double-Wert in den CustomData-Komponenten eines ItemStacks
     */
    public static void setDouble(ItemStack stack, String key, double value) {
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        CompoundTag tag = customData != null ? customData.copyTag() : new CompoundTag();
        tag.putDouble(key, value);
        stack.set(DataComponents.CUSTOM_DATA, CustomData.of(tag));
    }
    
    /**
     * Setzt einen Boolean-Wert in den CustomData-Komponenten eines ItemStacks
     */
    public static void setBoolean(ItemStack stack, String key, boolean value) {
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        CompoundTag tag = customData != null ? customData.copyTag() : new CompoundTag();
        tag.putBoolean(key, value);
        stack.set(DataComponents.CUSTOM_DATA, CustomData.of(tag));
    }
    
    /**
     * Setzt den CustomModelData-Wert eines ItemStacks
     */
    public static void setCustomModelData(ItemStack stack, int value) {
        stack.set(DataComponents.CUSTOM_MODEL_DATA, new CustomModelData(value));
    }
}
