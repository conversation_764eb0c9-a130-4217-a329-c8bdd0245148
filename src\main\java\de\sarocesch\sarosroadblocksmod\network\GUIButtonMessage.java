package de.sarocesch.sarosroadblocksmod.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.codec.StreamCodec;
import net.minecraft.network.protocol.common.custom.CustomPacketPayload;
import net.minecraft.resources.ResourceLocation;
import net.neoforged.neoforge.network.handling.IPayloadContext;
import net.neoforged.fml.common.Mod;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.neoforge.network.event.RegisterPayloadHandlersEvent;
import net.neoforged.fml.common.EventBusSubscriber;

import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.core.BlockPos;

import de.sarocesch.sarosroadblocksmod.world.inventory.GUIMenu;
import de.sarocesch.sarosroadblocksmod.procedures.TTKProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTJProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTHProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTGProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTFProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTDProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TTAProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.TT1Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P30Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P2Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P29Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P28Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P27Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P26Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P24Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P23Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P110Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.P108Procedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemRemoveProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemGradecProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemGradEckProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemEckeProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemCrossProcedure;
import de.sarocesch.sarosroadblocksmod.procedures.ItemBasicProcedure;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

import java.util.HashMap;

@EventBusSubscriber(bus = EventBusSubscriber.Bus.MOD, modid = SarosRoadBlocksModMod.MODID)
public class GUIButtonMessage implements CustomPacketPayload {
	public static final ResourceLocation ID = ResourceLocation.fromNamespaceAndPath(SarosRoadBlocksModMod.MODID, "gui_button_message");
	public static final Type<GUIButtonMessage> TYPE = new Type<>(ID);

	private final int buttonID, x, y, z;

	public GUIButtonMessage(int buttonID, int x, int y, int z) {
		this.buttonID = buttonID;
		this.x = x;
		this.y = y;
		this.z = z;
	}

	public static final StreamCodec<FriendlyByteBuf, GUIButtonMessage> STREAM_CODEC = StreamCodec.of(
			(buf, message) -> message.write(buf),
			buf -> new GUIButtonMessage(buf.readInt(), buf.readInt(), buf.readInt(), buf.readInt())
	);

	public void write(FriendlyByteBuf buffer) {
		buffer.writeInt(this.buttonID);
		buffer.writeInt(this.x);
		buffer.writeInt(this.y);
		buffer.writeInt(this.z);
	}

	@Override
	public Type<? extends CustomPacketPayload> type() {
		return TYPE;
	}

	public static void handle(GUIButtonMessage message, IPayloadContext context) {
		context.enqueueWork(() -> {
			Player entity = context.player();
			int buttonID = message.buttonID;
			int x = message.x;
			int y = message.y;
			int z = message.z;
			handleButtonAction(entity, buttonID, x, y, z);
		});
	}

	public static void handleButtonAction(Player entity, int buttonID, int x, int y, int z) {
		Level world = entity.level();
		HashMap guistate = GUIMenu.guistate;
		// Sicherheitsüberprüfung gegen willkürliche Chunk-Generierung
		if (!world.hasChunkAt(BlockPos.containing(x, y, z)))
			return;
		if (buttonID == 0) {
			ItemBasicProcedure.execute(entity);
		}
		if (buttonID == 1) {
			ItemRemoveProcedure.execute(entity);
		}
		if (buttonID == 2) {
			ItemGradecProcedure.execute(entity);
		}
		if (buttonID == 3) {
			ItemEckeProcedure.execute(entity);
		}
		if (buttonID == 4) {
			ItemGradEckProcedure.execute(entity);
		}
		if (buttonID == 5) {
			ItemCrossProcedure.execute(entity);
		}
		if (buttonID == 6) {
			P23Procedure.execute(entity);
		}
		if (buttonID == 7) {
			P24Procedure.execute(entity);
		}
		if (buttonID == 8) {
			P26Procedure.execute(entity);
		}
		if (buttonID == 9) {
			P27Procedure.execute(entity);
		}
		if (buttonID == 10) {
			P28Procedure.execute(entity);
		}
		if (buttonID == 11) {
			P29Procedure.execute(entity);
		}
		if (buttonID == 12) {
			P30Procedure.execute(entity);
		}
		if (buttonID == 13) {
			P108Procedure.execute(entity);
		}
		if (buttonID == 14) {
			P110Procedure.execute(entity);
		}
		if (buttonID == 15) {
			P2Procedure.execute(entity);
		}
		if (buttonID == 16) {
			TTAProcedure.execute(entity);
		}
		if (buttonID == 17) {
			TT1Procedure.execute(entity);
		}
		if (buttonID == 18) {
			TTDProcedure.execute(entity);
		}
		if (buttonID == 19) {
			TTFProcedure.execute(entity);
		}
		if (buttonID == 20) {
			TTGProcedure.execute(entity);
		}
		if (buttonID == 21) {
			TTHProcedure.execute(entity);
		}
		if (buttonID == 22) {
			TTJProcedure.execute(entity);
		}
		if (buttonID == 23) {
			TTKProcedure.execute(entity);
		}
	}

	@SubscribeEvent
	public static void registerMessage(RegisterPayloadHandlersEvent event) {
		event.registrar(SarosRoadBlocksModMod.MODID)
				.playToServer(TYPE, STREAM_CODEC, GUIButtonMessage::handle);
	}
}