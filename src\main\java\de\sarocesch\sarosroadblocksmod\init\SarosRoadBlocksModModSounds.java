package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.minecraft.core.registries.BuiltInRegistries;

import net.minecraft.sounds.SoundEvent;
import net.minecraft.resources.ResourceLocation;

import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModSounds {
	public static final DeferredRegister<SoundEvent> REGISTRY = DeferredRegister.create(BuiltInRegistries.SOUND_EVENT, SarosRoadBlocksModMod.MODID);
	public static final DeferredHolder<SoundEvent, SoundEvent> STREICHEN = REGISTRY.register("streichen", () -> SoundEvent.createVariableRangeEvent(ResourceLocation.fromNamespaceAndPath("saros_road_blocks_mod", "streichen")));
}
