
/*
 *    MCreator note: This file will be REGENERATED on each build.
 */
package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;

import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.network.chat.Component;
import net.minecraft.core.registries.Registries;

import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModTabs {
	public static final DeferredRegister<CreativeModeTab> REGISTRY = DeferredRegister.create(Registries.CREATIVE_MODE_TAB, SarosRoadBlocksModMod.MODID);
	public static final DeferredHolder<CreativeModeTab, CreativeModeTab> SAROS_ROAD_BLOCKS_MOD = REGISTRY.register("saros_road_blocks_mod",
			() -> CreativeModeTab.builder().title(Component.translatable("item_group.saros_road_blocks_mod.saros_road_blocks_mod")).icon(() -> new ItemStack(SarosRoadBlocksModModItems.LOGO.get())).displayItems((parameters, tabData) -> {
				tabData.accept(SarosRoadBlocksModModBlocks.ASPHALT.get().asItem());
				tabData.accept(SarosRoadBlocksModModItems.BRUSH.get());
				tabData.accept(SarosRoadBlocksModModItems.HAMMER.get());
				tabData.accept(SarosRoadBlocksModModItems.FILLING_TOOL.get());
				tabData.accept(SarosRoadBlocksModModBlocks.CRASH_BARRIER.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.CRASH_BARRIER_POLE.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.STREET_LIGHT.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.STREET_LIGHT_POLE.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.CRASH_BARROER_CORNER.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.CRASH_BARRIER_CORNER_2.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.SIDEWALK.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.GUARD_RAIL_END.get().asItem());
				tabData.accept(SarosRoadBlocksModModBlocks.GUARD_RAIL_END_2.get().asItem());
			})

					.build());
}
