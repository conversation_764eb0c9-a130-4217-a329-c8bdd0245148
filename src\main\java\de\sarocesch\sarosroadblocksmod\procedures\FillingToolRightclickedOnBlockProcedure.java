package de.sarocesch.sarosroadblocksmod.procedures;

import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundSource;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.core.Direction;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock.Variant;
import de.sarocesch.sarosroadblocksmod.block.SidewalkBlock;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlocks;
import net.minecraft.network.chat.Component;
import de.sarocesch.sarosroadblocksmod.util.RoadTextureManager;

public class FillingToolRightclickedOnBlockProcedure {

    public static void execute(Level world, double x, double y, double z, Player player) {
        BlockPos pos = new BlockPos((int) x, (int) y, (int) z);
        BlockState state = world.getBlockState(pos);

        // Prüfen, ob der Block ein AsphaltBlock ist
        if (state.getBlock() instanceof AsphaltBlock) {
            handleAsphalt(world, pos, state, player);
        }
        // Prüfen, ob der Block ein SidewalkBlock ist
        else if (state.getBlock() instanceof SidewalkBlock) {
            handleSidewalk(world, pos, state, player);
        }
    }

    private static void handleAsphalt(Level world, BlockPos pos, BlockState state, Player player) {
        // Aktuellen Variant und FACING (anstatt ROTATION) holen
        Variant currentVariant = state.getValue(AsphaltBlock.VARIANT);
        Direction currentFacing = state.getValue(AsphaltBlock.FACING);

        // Bestimmen des vorherigen Variants (in umgekehrter Reihenfolge)
        Variant nextVariant = switch (currentVariant) {
            case FIFTEEN -> Variant.FOURTEEN;
            case FOURTEEN -> Variant.THIRTEEN;
            case THIRTEEN -> Variant.TWELVE;
            case TWELVE -> Variant.ELEVEN;
            case ELEVEN -> Variant.TEN;
            case TEN -> Variant.NINE;
            case NINE -> Variant.EIGHT;
            case EIGHT -> Variant.SEVEN;
            case SEVEN -> Variant.SIX;
            case SIX -> Variant.FIVE;
            case FIVE -> Variant.FOUR;
            case FOUR -> Variant.THREE;
            case THREE -> Variant.TWO;
            case TWO -> Variant.ONE;
            case ONE -> Variant.DEFAULT;
            case DEFAULT -> Variant.DEFAULT;
        };

        // Aktualisieren des Blockzustands, wenn sich der Variant ändert
        if (currentVariant != nextVariant) {
            // Behalte die aktuelle Textur, wenn vorhanden
            String currentTexture = RoadTextureManager.getTextureForBlock(world, pos);

            BlockState newState = state.setValue(AsphaltBlock.VARIANT, nextVariant)
                    .setValue(AsphaltBlock.FACING, currentFacing);
            world.setBlock(pos, newState, 3);

            // Setze die Textur wieder, falls sie vorhanden war
            if (currentTexture != null && !currentTexture.isEmpty()) {
                RoadTextureManager.setTextureForBlock(world, pos, currentTexture);
            }

            world.playSound(null, pos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);
        }

        // Speziellen Fall behandeln, bei dem der Variant DEFAULT ist und ein Block darüber platziert werden soll
        if (nextVariant == Variant.DEFAULT) {
            BlockPos abovePos = pos.above();
            BlockState aboveState = world.getBlockState(abovePos);

            // Prüfen, ob sich kein Block darüber befindet
            if (aboveState.isAir()) {
                // Prüfen, ob der Spieler im Kreativmodus ist oder einen Asphaltblock im Inventar hat
                if (player.isCreative() || player.getInventory().contains(new ItemStack(SarosRoadBlocksModModBlocks.ASPHALT.get().asItem()))) {
                    // Behalte die aktuelle Textur, wenn vorhanden
                    String currentTexture = RoadTextureManager.getTextureForBlock(world, pos);

                    BlockState newAboveState = SarosRoadBlocksModModBlocks.ASPHALT.get().defaultBlockState()
                            .setValue(AsphaltBlock.VARIANT, Variant.FIFTEEN)
                            .setValue(AsphaltBlock.TEXTURE_VARIANT, state.getValue(AsphaltBlock.TEXTURE_VARIANT))
                            .setValue(AsphaltBlock.FACING, currentFacing);
                    world.setBlock(abovePos, newAboveState, 3);

                    // Setze die Textur für den neuen Block, falls eine vorhanden war
                    if (currentTexture != null && !currentTexture.isEmpty()) {
                        RoadTextureManager.setTextureForBlock(world, abovePos, currentTexture);
                    }
                    world.playSound(null, abovePos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);

                    // Nur einen Asphaltblock aus dem Inventar des Spielers entfernen, wenn nicht im Kreativmodus
                    if (!player.isCreative()) {
                        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
                            ItemStack stack = player.getInventory().getItem(i);
                            if (stack.getItem() == SarosRoadBlocksModModBlocks.ASPHALT.get().asItem()) {
                                stack.shrink(1);
                                break;
                            }
                        }
                    }
                } else {
                    // Nachricht an den Spieler senden, wenn er keinen Asphaltblock hat
                    player.sendSystemMessage(Component.literal("§4You need an Asphalt block!"));
                }
            }
        }
    }

    private static void handleSidewalk(Level world, BlockPos pos, BlockState state, Player player) {
        // Ähnliche Logik wie bei AsphaltBlock, aber für SidewalkBlock
        SidewalkBlock.Variant currentVariant = state.getValue(SidewalkBlock.VARIANT);
        Direction currentFacing = state.getValue(SidewalkBlock.FACING);

        // Bestimmen des vorherigen Variants (in umgekehrter Reihenfolge)
        SidewalkBlock.Variant nextVariant = switch (currentVariant) {
            case FIFTEEN -> SidewalkBlock.Variant.FOURTEEN;
            case FOURTEEN -> SidewalkBlock.Variant.THIRTEEN;
            case THIRTEEN -> SidewalkBlock.Variant.TWELVE;
            case TWELVE -> SidewalkBlock.Variant.ELEVEN;
            case ELEVEN -> SidewalkBlock.Variant.TEN;
            case TEN -> SidewalkBlock.Variant.NINE;
            case NINE -> SidewalkBlock.Variant.EIGHT;
            case EIGHT -> SidewalkBlock.Variant.SEVEN;
            case SEVEN -> SidewalkBlock.Variant.SIX;
            case SIX -> SidewalkBlock.Variant.FIVE;
            case FIVE -> SidewalkBlock.Variant.FOUR;
            case FOUR -> SidewalkBlock.Variant.THREE;
            case THREE -> SidewalkBlock.Variant.TWO;
            case TWO -> SidewalkBlock.Variant.ONE;
            case ONE -> SidewalkBlock.Variant.DEFAULT;
            case DEFAULT -> SidewalkBlock.Variant.DEFAULT;
        };

        // Aktualisieren des Blockzustands, wenn sich der Variant ändert
        if (currentVariant != nextVariant) {
            BlockState newState = state.setValue(SidewalkBlock.VARIANT, nextVariant)
                    .setValue(SidewalkBlock.FACING, currentFacing);
            world.setBlock(pos, newState, 3);
            world.playSound(null, pos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);
        }

        // Speziellen Fall behandeln, bei dem der Variant DEFAULT ist und ein Block darüber platziert werden soll
        if (nextVariant == SidewalkBlock.Variant.DEFAULT) {
            BlockPos abovePos = pos.above();
            BlockState aboveState = world.getBlockState(abovePos);

            // Prüfen, ob sich kein Block darüber befindet
            if (aboveState.isAir()) {
                // Prüfen, ob der Spieler im Kreativmodus ist oder einen SidewalkBlock im Inventar hat
                if (player.isCreative() || player.getInventory().contains(new ItemStack(SarosRoadBlocksModModBlocks.SIDEWALK.get().asItem()))) {
                    BlockState newAboveState = SarosRoadBlocksModModBlocks.SIDEWALK.get().defaultBlockState()
                            .setValue(SidewalkBlock.VARIANT, SidewalkBlock.Variant.FIFTEEN)
                            .setValue(SidewalkBlock.FACING, currentFacing);
                    world.setBlock(abovePos, newAboveState, 3);
                    world.playSound(null, abovePos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);

                    // Nur einen SidewalkBlock aus dem Inventar des Spielers entfernen, wenn nicht im Kreativmodus
                    if (!player.isCreative()) {
                        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
                            ItemStack stack = player.getInventory().getItem(i);
                            if (stack.getItem() == SarosRoadBlocksModModBlocks.SIDEWALK.get().asItem()) {
                                stack.shrink(1);
                                break;
                            }
                        }
                    }
                } else {
                    // Nachricht an den Spieler senden, wenn er keinen SidewalkBlock hat
                    player.sendSystemMessage(Component.literal("§4You need a Sidewalk block!"));
                }
            }
        }
    }
}
