package de.sarocesch.sarosroadblocksmod.item;

import net.minecraft.world.InteractionResult;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.Level;
import net.minecraft.world.entity.player.Player;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.context.UseOnContext;
import net.minecraft.nbt.CompoundTag;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock;
import net.minecraft.client.resources.language.I18n;
import de.sarocesch.sarosroadblocksmod.world.inventory.GUIMenu;
import net.minecraft.sounds.SoundSource;
import net.minecraft.resources.ResourceLocation;
import de.sarocesch.sarosroadblocksmod.util.RoadTextureManager;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.core.component.DataComponents;
import net.minecraft.world.item.component.CustomData;
import net.minecraft.world.item.component.CustomModelData;
import com.google.gson.Gson;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.SimpleMenuProvider;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.List;
import net.neoforged.fml.loading.FMLPaths; // NeoForge Pfade
import net.minecraft.core.Direction;
import net.minecraft.core.registries.BuiltInRegistries; // Ersatz für ForgeRegistries
import net.minecraft.sounds.SoundEvent;

public class BrushItem extends Item {
    public BrushItem() {
        super(new Item.Properties().stacksTo(1).rarity(Rarity.UNCOMMON));
    }

    @Override
    public void appendHoverText(ItemStack itemstack, TooltipContext context, List<Component> list, TooltipFlag flag) {
        super.appendHoverText(itemstack, context, list, flag);
        list.add(Component.literal(I18n.get("tooltip.sarosroadblocksmod.brushitem.key")));
    }

    private boolean isPainted(ItemStack stack) {
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        if (customData != null) {
            CompoundTag tag = customData.copyTag();
            return tag.contains("Painted") && tag.getBoolean("Painted");
        }
        return false;
    }

    private void togglePainted(ItemStack stack) {
        boolean painted = !isPainted(stack);

        // Erstelle oder hole das bestehende Tag
        CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
        CompoundTag tag = customData != null ? customData.copyTag() : new CompoundTag();

        // Setze die Daten
        tag.putBoolean("Painted", painted);
        stack.set(DataComponents.CUSTOM_DATA, CustomData.of(tag));

        // Setze CustomModelData als separate Komponente
        if (painted) {
            stack.set(DataComponents.CUSTOM_MODEL_DATA, new CustomModelData(1));
        } else {
            stack.set(DataComponents.CUSTOM_MODEL_DATA, new CustomModelData(0));
        }
    }

    @Override
    public InteractionResult useOn(UseOnContext context) {
        Player player = context.getPlayer();
        Level world = context.getLevel();
        ItemStack stack = context.getItemInHand();

        if (player != null && player.isShiftKeyDown()) {
            if (!world.isClientSide()) {
                player.openMenu(new SimpleMenuProvider(
                        (windowId, playerInventory, playerEntity) -> new GUIMenu(windowId, playerInventory, null),
                        Component.translatable("gui.saros_road_blocks_mod.gui")
                ));
            }
            return InteractionResult.SUCCESS;
        }

        BlockPos pos = context.getClickedPos();
        BlockState blockState = world.getBlockState(pos);

        if (blockState.getBlock() instanceof AsphaltBlock) {
            AsphaltBlock asphaltBlock = (AsphaltBlock) blockState.getBlock();
            String modi = "";
            CustomData customData = stack.get(DataComponents.CUSTOM_DATA);
            if (customData != null) {
                CompoundTag tag = customData.copyTag();
                if (tag.contains("modi")) {
                    modi = tag.getString("modi");
                }
            }

            BlockState newState = blockState;
            boolean textureChanged = false;

            if ("rotate".equals(modi)) {
                newState = blockState.rotate(world, pos, Rotation.CLOCKWISE_90);
            } else if ("remove".equals(modi)) {
                // Zwei Ansätze: Behalte den BlockState für Kompatibilität und setze auch die BlockEntity
                newState = blockState.setValue(AsphaltBlock.TEXTURE_VARIANT, AsphaltBlock.TextureVariant.DEFAULT);

                // Setze die Textur in der BlockEntity
                RoadTextureManager.setTextureForBlock(world, pos, "default");
                textureChanged = true;

                // Sende ein explizites Update an alle Clients
                if (!world.isClientSide()) {
                    world.sendBlockUpdated(pos, blockState, newState, 3);
                }
            } else {
                try {
                    // Behalte den BlockState für Kompatibilität
                    AsphaltBlock.TextureVariant variant = AsphaltBlock.TextureVariant.valueOf(modi.toUpperCase());
                    newState = blockState.setValue(AsphaltBlock.TEXTURE_VARIANT, variant);

                    // Blockausrichtung an die Blickrichtung des Spielers anpassen
                    Direction playerFacing = player.getDirection();
                    newState = newState.setValue(AsphaltBlock.FACING, playerFacing);

                    // Setze die Textur in der BlockEntity
                    RoadTextureManager.setTextureForBlock(world, pos, modi.toLowerCase());
                    textureChanged = true;

                    // Sende ein explizites Update an alle Clients
                    if (!world.isClientSide()) {
                        world.sendBlockUpdated(pos, blockState, newState, 3);
                    }

                    // Sound nur abspielen, wenn es eine spezifische TextureVariant ist (nicht rotate oder remove)
                    if (newState != blockState || textureChanged) {
                        playPaintSound(world, pos);
                    }
                } catch (IllegalArgumentException e) {
                    return InteractionResult.PASS; // Kein Update, wenn Modi ungültig ist
                }
            }

            if (newState != blockState || textureChanged) {
                // Setze den Block mit Flag 3 (Update Clients + Physik)
                world.setBlock(pos, newState, 3);

                // Stelle sicher, dass die BlockEntity-Daten korrekt synchronisiert werden
                if (!world.isClientSide()) {
                    BlockEntity blockEntity = world.getBlockEntity(pos);
                    if (blockEntity != null) {
                        blockEntity.setChanged();
                        world.sendBlockUpdated(pos, newState, newState, 3);
                    }
                }

                return InteractionResult.SUCCESS;
            }
        }

        return InteractionResult.PASS;
    }

    /**
     * Spielt den Malgeräusch ab, wenn in der Konfiguration aktiviert
     */
    private void playPaintSound(Level world, BlockPos pos) {
        File configroads = new File((FMLPaths.GAMEDIR.get().toString() + "/config/"), File.separator + "saro\u00B4s_road_blocks_mod.json");
        try {
            BufferedReader bufferedReader = new BufferedReader(new FileReader(configroads));
            StringBuilder jsonstringbuilder = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                jsonstringbuilder.append(line);
            }
            bufferedReader.close();

            com.google.gson.JsonObject main = new Gson().fromJson(jsonstringbuilder.toString(), com.google.gson.JsonObject.class);

            if (main.get("sounds").getAsBoolean()) {
                ResourceLocation soundLocation = ResourceLocation.parse("saros_road_blocks_mod:streichen");

                // NeoForge-kompatible Sound-Überprüfung und Wiedergabe
                if (BuiltInRegistries.SOUND_EVENT.containsKey(soundLocation)) {
                    SoundEvent soundEvent = BuiltInRegistries.SOUND_EVENT.get(soundLocation);

                    if (!world.isClientSide()) {
                        world.playSound(null, pos, soundEvent, SoundSource.BLOCKS, 1.0F, 1.0F);
                    } else {
                        world.playLocalSound(
                                pos.getX(),
                                pos.getY(),
                                pos.getZ(),
                                soundEvent,
                                SoundSource.BLOCKS,
                                1.0F,
                                1.0F,
                                false
                        );
                    }
                }
            }
        } catch (Exception e) {
            // Fehlerbehandlung für das Lesen der Konfigurationsdatei
        }
    }
}