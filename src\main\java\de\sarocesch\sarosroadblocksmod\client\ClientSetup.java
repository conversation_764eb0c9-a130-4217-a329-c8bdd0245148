package de.sarocesch.sarosroadblocksmod.client;

import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.fml.event.lifecycle.FMLClientSetupEvent;
import net.neoforged.fml.common.Mod;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.api.distmarker.Dist;
import net.minecraft.client.renderer.blockentity.BlockEntityRenderers;

import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;
import de.sarocesch.sarosroadblocksmod.client.renderer.RoadBlockRenderer;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlockEntities;



/**
 * Client-seitige Initialisierung
 */
@EventBusSubscriber(
        modid = SarosRoadBlocksModMod.MODID,
        bus = EventBusSubscriber.Bus.MOD,
        value = Dist.CLIENT
)
public class ClientSetup {

    @SubscribeEvent
    public static void onClientSetup(FMLClientSetupEvent event) {
        event.enqueueWork(() -> {
            // Registriere den Renderer für die BlockEntity
            BlockEntityRenderers.register(SarosRoadBlocksModModBlockEntities.ROAD_BLOCK_ENTITY.get(), RoadBlockRenderer::new);
        });
    }
}