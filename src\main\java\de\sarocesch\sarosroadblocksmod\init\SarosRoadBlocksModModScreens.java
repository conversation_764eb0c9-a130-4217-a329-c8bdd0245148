package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.fml.common.Mod;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.api.distmarker.Dist;
import net.neoforged.neoforge.client.event.RegisterMenuScreensEvent;
import de.sarocesch.sarosroadblocksmod.client.gui.GUIScreen;

@EventBusSubscriber(bus = EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class SarosRoadBlocksModModScreens {
	@SubscribeEvent
	public static void clientLoad(RegisterMenuScreensEvent event) {
		// Registriere den Screen für das GUI-Menü
		event.register(SarosRoadBlocksModModMenus.GUI.get(), GUIScreen::new);
	}
}