package de.sarocesch.sarosroadblocksmod;

import net.neoforged.bus.api.IEventBus;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.Mod;
import net.neoforged.neoforge.common.NeoForge;
import net.neoforged.neoforge.event.tick.ServerTickEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModTabs;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModSounds;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModMenus;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModItems;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlocks;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlockEntities;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.List;
import java.util.Collection;
import java.util.ArrayList;
import java.util.AbstractMap;

@Mod(SarosRoadBlocksModMod.MODID)
public class SarosRoadBlocksModMod {
	public static final Logger LOGGER = LogManager.getLogger(SarosRoadBlocksModMod.class);
	public static final String MODID = "saros_road_blocks_mod";

	public SarosRoadBlocksModMod(IEventBus modEventBus) {
		// Registriere Deferred Register
		SarosRoadBlocksModModSounds.REGISTRY.register(modEventBus);
		SarosRoadBlocksModModBlocks.REGISTRY.register(modEventBus);
		SarosRoadBlocksModModBlockEntities.REGISTRY.register(modEventBus);
		SarosRoadBlocksModModItems.REGISTRY.register(modEventBus);
		SarosRoadBlocksModModTabs.REGISTRY.register(modEventBus);
		SarosRoadBlocksModModMenus.REGISTRY.register(modEventBus);

		// Registriere Event-Handler
		NeoForge.EVENT_BUS.register(this);
	}

	private static final Collection<AbstractMap.SimpleEntry<Runnable, Integer>> workQueue = new ConcurrentLinkedQueue<>();

	public static void queueServerWork(int tick, Runnable action) {
		workQueue.add(new AbstractMap.SimpleEntry<>(action, tick));
	}

	@SubscribeEvent
	public void onServerTick(ServerTickEvent.Post event) {
		List<AbstractMap.SimpleEntry<Runnable, Integer>> actions = new ArrayList<>();
		workQueue.forEach(work -> {
			work.setValue(work.getValue() - 1);
			if (work.getValue() == 0)
				actions.add(work);
		});
		actions.forEach(e -> e.getKey().run());
		workQueue.removeAll(actions);
	}
}