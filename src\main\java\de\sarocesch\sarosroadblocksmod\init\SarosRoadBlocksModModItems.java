
/*
 *    MCreator note: This file will be REGENERATED on each build.
 */
package de.sarocesch.sarosroadblocksmod.init;

import net.neoforged.neoforge.registries.DeferredRegister;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.minecraft.core.registries.BuiltInRegistries;

import net.minecraft.world.level.block.Block;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.BlockItem;

import de.sarocesch.sarosroadblocksmod.item.RotateToolItem;
import de.sarocesch.sarosroadblocksmod.item.RemoveToolItem;
import de.sarocesch.sarosroadblocksmod.item.LogoItem;
import de.sarocesch.sarosroadblocksmod.item.HammerItem;
import de.sarocesch.sarosroadblocksmod.item.FillingToolItem;
import de.sarocesch.sarosroadblocksmod.item.BrushItem;
import de.sarocesch.sarosroadblocksmod.item.BrushGradeItem;
import de.sarocesch.sarosroadblocksmod.item.BrushGradEckItem;
import de.sarocesch.sarosroadblocksmod.item.BrushFullItem;
import de.sarocesch.sarosroadblocksmod.item.BrushEckeItem;
import de.sarocesch.sarosroadblocksmod.SarosRoadBlocksModMod;

public class SarosRoadBlocksModModItems {
	public static final DeferredRegister<Item> REGISTRY = DeferredRegister.create(BuiltInRegistries.ITEM, SarosRoadBlocksModMod.MODID);
	public static final DeferredHolder<Item, Item> ASPHALT = block(SarosRoadBlocksModModBlocks.ASPHALT);
	public static final DeferredHolder<Item, Item> BRUSH = REGISTRY.register("brush", () -> new BrushItem());
	public static final DeferredHolder<Item, Item> HAMMER = REGISTRY.register("hammer", () -> new HammerItem());
	public static final DeferredHolder<Item, Item> FILLING_TOOL = REGISTRY.register("filling_tool", () -> new FillingToolItem());
	public static final DeferredHolder<Item, Item> CRASH_BARRIER = block(SarosRoadBlocksModModBlocks.CRASH_BARRIER);
	public static final DeferredHolder<Item, Item> CRASH_BARRIER_POLE = block(SarosRoadBlocksModModBlocks.CRASH_BARRIER_POLE);
	public static final DeferredHolder<Item, Item> STREET_LIGHT = block(SarosRoadBlocksModModBlocks.STREET_LIGHT);
	public static final DeferredHolder<Item, Item> STREET_LIGHT_POLE = block(SarosRoadBlocksModModBlocks.STREET_LIGHT_POLE);
	public static final DeferredHolder<Item, Item> CRASH_BARROER_CORNER = block(SarosRoadBlocksModModBlocks.CRASH_BARROER_CORNER);
	public static final DeferredHolder<Item, Item> CRASH_BARRIER_CORNER_2 = block(SarosRoadBlocksModModBlocks.CRASH_BARRIER_CORNER_2);
	public static final DeferredHolder<Item, Item> LOGO = REGISTRY.register("logo", () -> new LogoItem());
	public static final DeferredHolder<Item, Item> BRUSH_FULL = REGISTRY.register("brush_full", () -> new BrushFullItem());
	public static final DeferredHolder<Item, Item> BRUSH_GRADE = REGISTRY.register("brush_grade", () -> new BrushGradeItem());
	public static final DeferredHolder<Item, Item> BRUSH_ECKE = REGISTRY.register("brush_ecke", () -> new BrushEckeItem());
	public static final DeferredHolder<Item, Item> BRUSH_GRAD_ECK = REGISTRY.register("brush_grad_eck", () -> new BrushGradEckItem());
	public static final DeferredHolder<Item, Item> ROTATE_TOOL = REGISTRY.register("rotate_tool", () -> new RotateToolItem());
	public static final DeferredHolder<Item, Item> REMOVE_TOOL = REGISTRY.register("remove_tool", () -> new RemoveToolItem());
	public static final DeferredHolder<Item, Item> SIDEWALK = block(SarosRoadBlocksModModBlocks.SIDEWALK);
	public static final DeferredHolder<Item, Item> GUARD_RAIL_END = block(SarosRoadBlocksModModBlocks.GUARD_RAIL_END);
	public static final DeferredHolder<Item, Item> GUARD_RAIL_END_2 = block(SarosRoadBlocksModModBlocks.GUARD_RAIL_END_2);
	public static final DeferredHolder<Item, Item> STREET_LIGHT_POLE_2 = block(SarosRoadBlocksModModBlocks.STREET_LIGHT_POLE_2);

	private static DeferredHolder<Item, Item> block(DeferredHolder<Block, Block> block) {
		return REGISTRY.register(block.getId().getPath(), () -> new BlockItem(block.get(), new Item.Properties()));
	}
}
