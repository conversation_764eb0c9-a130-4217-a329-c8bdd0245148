package de.sarocesch.sarosroadblocksmod.procedures;

import net.minecraft.core.BlockPos;
import net.minecraft.sounds.SoundSource;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.core.Direction;
import de.sarocesch.sarosroadblocksmod.block.AsphaltBlock;
import de.sarocesch.sarosroadblocksmod.block.SidewalkBlock;
import de.sarocesch.sarosroadblocksmod.init.SarosRoadBlocksModModBlocks;
import de.sarocesch.sarosroadblocksmod.util.RoadTextureManager;

public class StufenProcedure {

    public static void execute(Level world, double x, double y, double z, Player player) {
        BlockPos pos = new BlockPos((int) x, (int) y, (int) z);
        BlockState state = world.getBlockState(pos);

        // Prüfen, ob der Block ein AsphaltBlock oder SidewalkBlock ist
        if (state.getBlock() instanceof AsphaltBlock asphaltBlock) {
            handleAsphalt(world, pos, state, player);
        } else if (state.getBlock() instanceof SidewalkBlock sidewalkBlock) {
            handleSidewalk(world, pos, state, player);
        }
    }

    // Behandlung für AsphaltBlock
    private static void handleAsphalt(Level world, BlockPos pos, BlockState state, Player player) {
        AsphaltBlock.Variant currentVariant = state.getValue(AsphaltBlock.VARIANT);
        Direction currentFacing = state.getValue(AsphaltBlock.FACING);

        if (currentVariant == AsphaltBlock.Variant.FIFTEEN) {
            BlockPos belowPos = pos.below();
            BlockState belowState = world.getBlockState(belowPos);

            if (belowState.getBlock() instanceof AsphaltBlock) {
                updateAsphaltBlock(world, belowPos, belowState, AsphaltBlock.Variant.DEFAULT, currentFacing);
                removeAndGiveItem(world, pos, player, new ItemStack(SarosRoadBlocksModModBlocks.ASPHALT.get()));
            }
        } else {
            AsphaltBlock.Variant nextVariant = getNextAsphaltVariant(currentVariant);
            updateAsphaltBlock(world, pos, state, nextVariant, currentFacing);
        }
    }

    // Behandlung für SidewalkBlock
    private static void handleSidewalk(Level world, BlockPos pos, BlockState state, Player player) {
        SidewalkBlock.Variant currentVariant = state.getValue(SidewalkBlock.VARIANT);
        Direction currentFacing = state.getValue(SidewalkBlock.FACING);

        if (currentVariant == SidewalkBlock.Variant.FIFTEEN) {
            BlockPos belowPos = pos.below();
            BlockState belowState = world.getBlockState(belowPos);

            if (belowState.getBlock() instanceof SidewalkBlock) {
                updateSidewalkBlock(world, belowPos, belowState, SidewalkBlock.Variant.DEFAULT, currentFacing);
                removeAndGiveItem(world, pos, player, new ItemStack(SarosRoadBlocksModModBlocks.SIDEWALK.get()));
            }
        } else {
            SidewalkBlock.Variant nextVariant = getNextSidewalkVariant(currentVariant);
            updateSidewalkBlock(world, pos, state, nextVariant, currentFacing);
        }
    }

    // Logik für den nächsten AsphaltBlock.Variant
    private static AsphaltBlock.Variant getNextAsphaltVariant(AsphaltBlock.Variant currentVariant) {
        return switch (currentVariant) {
            case DEFAULT -> AsphaltBlock.Variant.ONE;
            case ONE -> AsphaltBlock.Variant.TWO;
            case TWO -> AsphaltBlock.Variant.THREE;
            case THREE -> AsphaltBlock.Variant.FOUR;
            case FOUR -> AsphaltBlock.Variant.FIVE;
            case FIVE -> AsphaltBlock.Variant.SIX;
            case SIX -> AsphaltBlock.Variant.SEVEN;
            case SEVEN -> AsphaltBlock.Variant.EIGHT;
            case EIGHT -> AsphaltBlock.Variant.NINE;
            case NINE -> AsphaltBlock.Variant.TEN;
            case TEN -> AsphaltBlock.Variant.ELEVEN;
            case ELEVEN -> AsphaltBlock.Variant.TWELVE;
            case TWELVE -> AsphaltBlock.Variant.THIRTEEN;
            case THIRTEEN -> AsphaltBlock.Variant.FOURTEEN;
            case FOURTEEN -> AsphaltBlock.Variant.FIFTEEN;
            default -> AsphaltBlock.Variant.FIFTEEN;
        };
    }

    // Logik für den nächsten SidewalkBlock.Variant
    private static SidewalkBlock.Variant getNextSidewalkVariant(SidewalkBlock.Variant currentVariant) {
        return switch (currentVariant) {
            case DEFAULT -> SidewalkBlock.Variant.ONE;
            case ONE -> SidewalkBlock.Variant.TWO;
            case TWO -> SidewalkBlock.Variant.THREE;
            case THREE -> SidewalkBlock.Variant.FOUR;
            case FOUR -> SidewalkBlock.Variant.FIVE;
            case FIVE -> SidewalkBlock.Variant.SIX;
            case SIX -> SidewalkBlock.Variant.SEVEN;
            case SEVEN -> SidewalkBlock.Variant.EIGHT;
            case EIGHT -> SidewalkBlock.Variant.NINE;
            case NINE -> SidewalkBlock.Variant.TEN;
            case TEN -> SidewalkBlock.Variant.ELEVEN;
            case ELEVEN -> SidewalkBlock.Variant.TWELVE;
            case TWELVE -> SidewalkBlock.Variant.THIRTEEN;
            case THIRTEEN -> SidewalkBlock.Variant.FOURTEEN;
            case FOURTEEN -> SidewalkBlock.Variant.FIFTEEN;
            default -> SidewalkBlock.Variant.FIFTEEN;
        };
    }

    // Aktualisierung des AsphaltBlocks
    private static void updateAsphaltBlock(Level world, BlockPos pos, BlockState state, AsphaltBlock.Variant nextVariant, Direction facing) {
        // Behalte die aktuelle Textur, wenn vorhanden
        String currentTexture = RoadTextureManager.getTextureForBlock(world, pos);

        BlockState newState = state.setValue(AsphaltBlock.VARIANT, nextVariant)
                                   .setValue(AsphaltBlock.FACING, facing);
        world.setBlock(pos, newState, 3);

        // Setze die Textur wieder, falls sie vorhanden war
        if (currentTexture != null && !currentTexture.isEmpty()) {
            RoadTextureManager.setTextureForBlock(world, pos, currentTexture);
        }

        world.playSound(null, pos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);
    }

    // Aktualisierung des SidewalkBlocks
    private static void updateSidewalkBlock(Level world, BlockPos pos, BlockState state, SidewalkBlock.Variant nextVariant, Direction facing) {
        BlockState newState = state.setValue(SidewalkBlock.VARIANT, nextVariant)
                                   .setValue(SidewalkBlock.FACING, facing);
        world.setBlock(pos, newState, 3);
        world.playSound(null, pos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);
    }

    // Entfernen des Blocks und Hinzufügen zum Inventar
    private static void removeAndGiveItem(Level world, BlockPos pos, Player player, ItemStack itemStack) {
        world.removeBlock(pos, false);
        if (!player.isCreative()) {
            player.addItem(itemStack);
        }
        world.playSound(null, pos, SoundEvents.ANVIL_BREAK, SoundSource.BLOCKS, 1.0F, 1.0F);
    }
}
