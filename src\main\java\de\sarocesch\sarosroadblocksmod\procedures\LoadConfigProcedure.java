package de.sarocesch.sarosroadblocksmod.procedures;

import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.fml.loading.FMLPaths;
import net.neoforged.fml.event.lifecycle.FMLCommonSetupEvent;
import net.neoforged.fml.common.Mod;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.bus.api.Event;

import javax.annotation.Nullable;

import java.io.IOException;
import java.io.FileWriter;
import java.io.File;

import com.google.gson.GsonBuilder;
import com.google.gson.Gson;

@EventBusSubscriber(bus = EventBusSubscriber.Bus.MOD)
public class LoadConfigProcedure {
	@SubscribeEvent
	public static void init(FMLCommonSetupEvent event) {
		// Explizite Angabe als Runnable
		event.enqueueWork((Runnable) LoadConfigProcedure::execute);
	}

	public static void execute() {
		execute(null);
	}

	private static void execute(@Nullable Event event) {
		com.google.gson.JsonObject main = new com.google.gson.JsonObject();
		File configroads = new File("");
		File configsigns = new File("");
		configroads = new File((FMLPaths.GAMEDIR.get().toString() + "/config/"), File.separator + "saro\u00B4s_road_blocks_mod.json");
		if (!configroads.exists()) {
			try {
				configroads.getParentFile().mkdirs();
				configroads.createNewFile();
			} catch (IOException exception) {
				exception.printStackTrace();
			}
			main.addProperty("sounds", true);
			{
				Gson mainGSONBuilderVariable = new GsonBuilder().setPrettyPrinting().create();
				try {
					FileWriter fileWriter = new FileWriter(configroads);
					fileWriter.write(mainGSONBuilderVariable.toJson(main));
					fileWriter.close();
				} catch (IOException exception) {
					exception.printStackTrace();
				}
			}
		}
	}
}