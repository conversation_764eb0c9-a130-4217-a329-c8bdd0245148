package de.sarocesch.sarosroadblocksmod.block;

import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.StateDefinition;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.HorizontalDirectionalBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.EntityBlock;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.item.ItemStack;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.Level;

import java.util.List;
import java.util.Collections;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.util.StringRepresentable;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.VoxelShape;
import net.minecraft.world.level.storage.loot.LootParams;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.BlockEntityTicker;
import net.minecraft.world.level.block.entity.BlockEntityType;

import de.sarocesch.sarosroadblocksmod.block.entity.RoadBlockEntity;

import javax.annotation.Nullable;

public class AsphaltBlock extends Block implements EntityBlock {
    public static final DirectionProperty FACING = HorizontalDirectionalBlock.FACING;
public static final EnumProperty<Variant> VARIANT = EnumProperty.create("variant", Variant.class);
public static final EnumProperty<TextureVariant> TEXTURE_VARIANT = EnumProperty.create("texture_variant", TextureVariant.class);

    public AsphaltBlock() {
        super(BlockBehaviour.Properties.of().sound(SoundType.STONE).strength(3f, 20f));
        this.registerDefaultState(this.stateDefinition.any()
            .setValue(FACING, Direction.NORTH)
            .setValue(VARIANT, Variant.DEFAULT)
            .setValue(TEXTURE_VARIANT, TextureVariant.DEFAULT)
        );
    }

@Override
public VoxelShape getShape(BlockState state, BlockGetter world, BlockPos pos, CollisionContext context) {
    int height = getHeightForVariant(state.getValue(VARIANT));
    return switch (state.getValue(FACING)) {
        default -> box(0, 0, 0, 16, height, 16);
        case NORTH -> box(0, 0, 0, 16, height, 16);
        case EAST -> box(0, 0, 0, 16, height, 16);
        case WEST -> box(0, 0, 0, 16, height, 16);
    };
}

private int getHeightForVariant(Variant variant) {
    return switch (variant) {
        case ONE -> 15;
        case TWO -> 14;
        case THREE -> 13;
        case FOUR -> 12;
        case FIVE -> 11;
        case SIX -> 10;
        case SEVEN -> 9;
        case EIGHT -> 8;
        case NINE -> 7;
        case TEN -> 6;
        case ELEVEN -> 5;
        case TWELVE -> 4;
        case THIRTEEN -> 3;
        case FOURTEEN -> 2;
        case FIFTEEN -> 1;
        default -> 16; // Für "default" oder unbekannte Varianten
    };
}



    @Override
    protected void createBlockStateDefinition(StateDefinition.Builder<Block, BlockState> builder) {
        builder.add(FACING, VARIANT, TEXTURE_VARIANT);
    }

@Override
public BlockState getStateForPlacement(BlockPlaceContext context) {
    return this.defaultBlockState()
        .setValue(FACING, context.getHorizontalDirection().getOpposite())
        .setValue(VARIANT, Variant.DEFAULT)
        .setValue(TEXTURE_VARIANT, TextureVariant.DEFAULT);
}


    @Override
    public BlockState rotate(BlockState state, Rotation rot) {
        return state.setValue(FACING, rot.rotate(state.getValue(FACING)));
    }

    @Override
    public BlockState mirror(BlockState state, Mirror mirrorIn) {
        return state.rotate(mirrorIn.getRotation(state.getValue(FACING)));
    }

    @Override
    public List<ItemStack> getDrops(BlockState state, LootParams.Builder builder) {
        List<ItemStack> dropsOriginal = super.getDrops(state, builder);
        if (!dropsOriginal.isEmpty())
            return dropsOriginal;
        return Collections.singletonList(new ItemStack(this, 1));
    }

    // BlockEntity-Methoden
    @Override
    public BlockEntity newBlockEntity(BlockPos pos, BlockState state) {
        return new RoadBlockEntity(pos, state);
    }

    @Nullable
    @Override
    public <T extends BlockEntity> BlockEntityTicker<T> getTicker(Level level, BlockState state, BlockEntityType<T> type) {
        return null; // Kein Ticker benötigt, da keine regelmäßigen Updates erforderlich sind
    }

    /**@Override
    public InteractionResult use(BlockState state, Level world, BlockPos pos, Player player, InteractionHand hand, BlockHitResult hit) {
        if (!world.isClientSide) {
            // Check if the player is holding shift (sneaking)
            if (player.isShiftKeyDown()) {
                // Cycle the 'Variant' (height) when shift-right-clicking
                BlockState newState = state.cycle(VARIANT);
                world.setBlock(pos, newState, 3);
            } else {
                // Cycle the 'TextureVariant' (line) when right-clicking without shift
                BlockState newState = state.cycle(TEXTURE_VARIANT);
                world.setBlock(pos, newState, 3);
            }
        }
        return InteractionResult.SUCCESS;
    }*/

    public enum Variant implements StringRepresentable {
        DEFAULT("default"),
        ONE("1"),
        TWO("2"),
        THREE("3"),
        FOUR("4"),
        FIVE("5"),
        SIX("6"),
        SEVEN("7"),
        EIGHT("8"),
        NINE("9"),
        TEN("10"),
        ELEVEN("11"),
        TWELVE("12"),
        THIRTEEN("13"),
        FOURTEEN("14"),
        FIFTEEN("15");

        private final String name;

        Variant(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }
    }

    public enum TextureVariant implements StringRepresentable {
        DEFAULT("default"),
        STRAIGHT("straight"),
        CORNER("corner"),
        CROSS("cross"),
        TWO("two"),
        TT("tt"),
        TF("tf"),
        TS("ts"),
        TSE("tse"),
        TE("te"),
        NT("nt"),
        THIRTY("thirty"),
        OHAE("ohae"),
        OHAT("ohat"),
        TTA("tta"),
        TTS("tts"),
        TTD("ttd"),
        TTF("ttf"),
        TTG("ttg"),
        TTH("tth"),
        TTJ("ttj"),
        TTK("ttk"),
        CORNER_STRAIGHT("corner_straight");

        private final String name;

        TextureVariant(String name) {
            this.name = name;
        }

        @Override
        public String getSerializedName() {
            return this.name;
        }
    }
}
